//Hooks
import React, { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useCookies } from 'next-client-cookies'

//Ccmponents
import ExoFromDb from './tabs/exo-from-db/exo-from-db'
import ExoFromFile from './tabs/exo-from-file/exo-from-file'
import ExoFromExam from './tabs/exo-from-exam/exo-from-exam'
import BetaPopup from './tabs/beta-popup'

//Icons
import { UploadFile } from '@/components/icons'
import { BookOpenText, ChevronLeft, Database, Users } from 'lucide-react'

//UI
import { Button } from '@/components/ui/button'

//Store
import { useLocalStorageStore } from '@/lib/stores/local-storage-store/local-storage.store'
import { selectUseExoModeStore } from '@/lib/stores/exercise-mode-store/exercise-store'
import useAccountStore from '@/lib/stores/account-store/store'

//Prisma-Schema
import { ExamTypeType } from '@/prisma/generated/zod/inputTypeSchemas/ExamTypeSchema'
import { Domain, FeatureFlagName } from '@prisma/client'
import { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'

//Types
import { Chapter, Part } from '@/lib/training-mode/types'

//API-function-call
import {
    ExoOutput,
    ExoSortsAndFilters,
    ExoSubject,
    getSubjectsByDomainIdAndLevelId
} from '@/lib/exams/actions'
import { getExamTypeByLevelIdAndDomainId } from '@/lib/domain-level/actions'
import { getFeaturesFlagsByNames } from '@/lib/features/actions'
import { toast } from 'sonner'

interface ExoGeneratorProps {
    getDomains: (
        level: string
    ) => Promise<Domain[] | { error: string; status: number }>
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
    getSubjectsByDomainIdAndLevelId: (
        domainId: number,
        levelId: number | null
    ) => Promise<ExoSubject[]>
}

function ExoGenerator({
    getDomains,
    getChapters,
    getParts,
    getSubjectsByDomainIdAndLevelId
}: ExoGeneratorProps) {
    const [activeTab, setActiveTab] = useState('')
    const [isExamMode, setIsExamMode] = useState<boolean | null>()
    const [domains, setDomains] = useState<Domain[]>([])
    const domain = selectUseExoModeStore.use.setSubject()
    const setLevel = selectUseExoModeStore.use.setLevel()
    const setMode = selectUseExoModeStore.use.setMode()
    const { user } = useAccountStore()
    const t = useTranslations('app.mode.exo.tab')
    const [examType, setExamType] = React.useState<ExamTypeType[]>([])
    const domainId = useLocalStorageStore(state => state.domainId)
    const cookies = useCookies()
    const topic = cookies.get('topic')
    const levelIdFromCookie = cookies.get('levelId')

    useEffect(() => {
        const examModeEnabled = async () => {
            const featureFlags = await getFeaturesFlagsByNames([
                FeatureFlagName.STUDENT_EXAM_MODE
            ])

            if (!Array.isArray(featureFlags)) {
                return null
            }

            const examModeEnabled =
                featureFlags?.find(
                    flag =>
                        flag.featureName === FeatureFlagName.STUDENT_EXAM_MODE
                )?.isEnabled || false

            setIsExamMode(examModeEnabled)
        }
        examModeEnabled()
    }, [])

    useEffect(() => {
        setLevel(user.userLevel as Level)
        domain(topic!)
        ;(async () => {
            const domains = await getDomains(levelIdFromCookie!)
            if ('error' in domains) {
                toast.error('Erreur fetching domains')
                return // Exit if there's an error
            }
            setDomains(domains)
        })()
    }, [levelIdFromCookie])

    useEffect(() => {
        switch (activeTab) {
            case 'DB':
                setMode('FROM_DB')
                break
            case 'FILE':
                setMode('FROM_FILE')
                break
            case 'EXAM':
                setMode('FROM_EXAM')
                break
        }
    }, [activeTab])

    useEffect(() => {
        ;(async () => {
            if (domainId && user.userLevel) {
                const examtype = await getExamTypeByLevelIdAndDomainId(
                    user.userLevel?.id,
                    domainId
                )
                if (examtype && 'error' in examtype) throw new Error()
                setExamType(examtype!)
            }
        })()
    }, [domainId, levelIdFromCookie])

    const handleTabClick = (tabName: string) => {
        setActiveTab(tabName)
    }

    const handleBackClick = () => {
        setActiveTab('')
    }

    // Tab content mapping
    const tabContents = {
        DB: (
            <ExoFromDb
                domains={domains}
                getChapters={getChapters}
                getParts={getParts}
            />
        ),
        FILE: <ExoFromFile />,
        EXAM: (
            <ExoFromExam
                domains={domains}
                getSubjectsByDomainIdAndLevelId={
                    getSubjectsByDomainIdAndLevelId
                }
            />
        )
    }

    return (
        <div className="w-full h-auto relative flex flex-col gap-12 mb-3 items-center">
            <h2 className="text-2xl md:text-3xl text-dinoBotVibrantBlue leading-9 font-extrabold text-center underline 2xl:mb-5">
                {t('title')}
            </h2>

            {/* Back button positioned to the left outside of the bordered div */}
            <div className="w-full flex items-start gap-4">
                {activeTab !== '' && (
                    <Button
                        onClick={handleBackClick}
                        className="px-4 py-2 bg-dinoBotBlue hover:bg-dinoBotBlue rounded-full"
                        size="icon"
                    >
                        <ChevronLeft
                            className="text-white !w-8 !h-8"
                            strokeWidth={2.5}
                        />
                    </Button>
                )}

                {/* Show tab buttons when no tab is selected */}
                <div
                    className={`border border-dinoBotVibrantBlue bg-white w-[850px] ${activeTab === '' ? 'flex items-center justify-center xl:h-[400px] 2xl:h-[550px]' : ''}`}
                >
                    {activeTab === '' && (
                        <div>
                            <h2 className="text-dinoBotGray text-lg font-bold text-center mb-8">
                                {t('secondTitle')}
                            </h2>
                            <div className="flex flex-row gap-4 justify-around items-center border-2 border-dinoBotGray/60 rounded-lg">
                                <div
                                    className={`flex flex-col items-center p-4 cursor-pointer`}
                                    onClick={() => handleTabClick('DB')}
                                >
                                    <Database
                                        className={`w-14 h-14 mb-2 bg-dinoBotLightBlue rounded-full p-3 text-black`}
                                    />
                                    <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                                        <p className="font-medium">
                                            {t('db.title')}
                                        </p>
                                    </span>
                                </div>

                                <BetaPopup feature="FILE">
                                    <div
                                        className={`flex flex-col items-center p-4`}
                                        onClick={() => handleTabClick('FILE')}
                                    >
                                        <UploadFile
                                            className={`size-14 mb-2 bg-dinoBotLightBlue rounded-full p-[10px] overflow-visible text-black`}
                                        />
                                        <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                                            <p className="font-medium">
                                                {t('file.title')}
                                            </p>
                                        </span>
                                    </div>
                                </BetaPopup>
                                {isExamMode &&
                                    (examType && examType[0] ? (
                                        <BetaPopup feature="EXAM">
                                            <div
                                                onClick={() =>
                                                    handleTabClick('EXAM')
                                                }
                                                className={`flex flex-col items-center p-4`}
                                            >
                                                <BookOpenText
                                                    className={`w-14 h-14 mb-2 bg-dinoBotLightBlue overflow-visible rounded-full p-3 text-black`}
                                                />
                                                <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                                                    <p className="font-medium">
                                                        {t('exam.title')}
                                                    </p>
                                                </span>
                                            </div>
                                        </BetaPopup>
                                    ) : null)}
                            </div>
                        </div>
                    )}

                    {/* Show selected tab content using table lookup */}
                    {activeTab !== '' && (
                        <div className="flex flex-col items-center w-full h-full">
                            <div
                                className={`h-full px-10 pt-11 bg-clip-padding backdrop-blur-md backdrop-opacity-90 bg-white/60 saturate-100 backdrop-contrast-100 w-[350px] sm:w-[500px] md:w-[600px] lg:w-[845px] ${
                                    activeTab === 'FILE'
                                        ? 'pb-8 xl:pb-4 2xl:py-8'
                                        : activeTab === 'EXAM'
                                          ? 'pb-4 2xl:py-8'
                                          : 'pb-4'
                                }`}
                            >
                                {
                                    tabContents[
                                        activeTab as keyof typeof tabContents
                                    ]
                                }
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default ExoGenerator
