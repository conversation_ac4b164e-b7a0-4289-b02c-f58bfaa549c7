import * as React from 'react'
import { <PERSON> } from '@/i18n/routing'
import { auth } from '@/auth'
import { Button } from '@/components/ui/button'
import { UserMenu } from '@/components/header/user-menu'
import { SidebarMobile } from '../sidebar/sidebar-mobile'
import { FilesSidebarMobile } from '../sidebar/files-sidebar-mobile'
import { SidebarToggle } from '../sidebar/sidebar-toggle'
import { FilesSidebarToggle } from '../sidebar/files-sidebar-toggle'
import { ChatHistory } from '../chat/chat-history'
import { FileHistory } from '../files/file-history'
import { Session } from '@/lib/types'
import DinoBotLogo from 'public/dinobot-logo-small.svg'
import Image from 'next/image'
import { SelectTopic } from '../select/select-topic'
import { LogIn } from 'lucide-react'
import { signOut } from '@/auth'
import { getUser } from '@/auth'
import PopulateUser from '../account-sections/profile/populate-user'
import { revalidatePath } from 'next/cache'
import { getUserPrompts } from '@/lib/admin/actions'
import DisconnectButton from '../login/disconnect'
import SelectInlt from '../i18n/select-inlt'
import { getLocale, getTranslations } from 'next-intl/server'
import SwitchAvatar from '../avatar/components/switch-avatar'
import { getFeaturesFlagsByNames } from '@/lib/features/actions'
import { FeatureFlagName } from '@prisma/client'
import SplitScreen from '@/app/[locale]/(app)/(mode)/exercise/train/components/split-screen'
import { Select, SelectTrigger, SelectValue } from '@/components/ui/select'
import IconDocDetails from 'public/icon _doc_detail_.svg'
import { getLangDir } from 'rtl-detect'

async function UserSection() {
    const session = (await auth()) as Session
    const user = await getUser(session?.user?.email)

    // console.log("User",user)

    const userPrompts = user?.isAdmin ? await getUserPrompts(user.id) : null

    return (
        <>
            <UserMenu user={session.user} />
            {session?.user && user ? (
                <PopulateUser retrievedUser={user} userPrompts={userPrompts} />
            ) : null}
        </>
    )
}

function getFeatureFlagByName(
    featureName: string[],
    features: { featureName: FeatureFlagName; isEnabled: boolean }[]
) {
    return features.find(feature => featureName.includes(feature.featureName))
        ?.isEnabled
}

async function UserOrLogin({
    isChatModeEnabled
}: {
    isChatModeEnabled: boolean
}) {
    const session = (await auth()) as Session
    const t = await getTranslations('app.headers.login')
    const locale = await getLocale()
    return (
        <div
            className={`flex flex-row justify-center items-center ${locale === 'ar' ? 'gap-4' : ''}`}
        >
            {session?.user ? (
                <>
                    {isChatModeEnabled ? (
                        <>
                            <SidebarMobile>
                                <ChatHistory userId={session.user.id} />
                            </SidebarMobile>
                            <SidebarToggle />
                        </>
                    ) : null}
                </>
            ) : (
                <Link href="/" rel="nofollow">
                    <Image
                        src={DinoBotLogo}
                        alt="DinoBot Logo"
                        className="min-w-[40px] w-[40px] sm:mr-2"
                    />
                </Link>
            )}
            <div className="flex items-center ">
                {/* <IconSeparator className="size-6 text-muted-foreground/50" /> */}
                {session?.user ? (
                    <UserSection />
                ) : (
                    <Button
                        asChild
                        className="px-2 mb-1 bg-transparent border border-gray-500 rounded-2xl text-gray-500 hover:bg-gray-500 hover:text-white sm:ml-2 transition-all duration-300 "
                    >
                        <Link href="/login">
                            <LogIn className="block sm:hidden size-4" />
                            <span className="hidden sm:block">
                                {t('title')}
                            </span>
                        </Link>
                    </Button>
                )}
            </div>
        </div>
    )
}

async function FilesSideBar() {
    const session = (await auth()) as Session

    return (
        <>
            {session?.user ? (
                <>
                    <FilesSidebarMobile>
                        <FileHistory userId={session.user.id} />
                    </FilesSidebarMobile>
                    <FilesSidebarToggle />
                </>
            ) : null}
        </>
    )
}

export async function Header({ loginType = true }: { loginType?: boolean }) {
    const t = await getTranslations('app.headers.mode')
    const session = (await auth()) as Session
    const user = await getUser(session?.user?.email)
    const locale = await getLocale()
    const dir = getLangDir(locale)
    const avatarModeEnabled = process.env.AVATAR_MODE === 'true' || false
    const chatModeExamModeFlag = (await getFeaturesFlagsByNames([
        FeatureFlagName.STUDENT_CHAT_MODE,
        FeatureFlagName.STUDENT_EXAM_MODE
    ])) as { featureName: FeatureFlagName; isEnabled: boolean }[]

    return (
        <header className="sticky top-0 z-10 flex items-center h-16 md:h-16 px-4 border-b shrink-0 bg-gradient-to-b from-background/10 via-background/50 to-background/80 backdrop-blur-xl md:grid md:grid-cols-8 shadow-lg shadow-dinoBotCyan/15 flex-row justify-between">
            <div className="sm:w-fit flex items-center col-span-2">
                {loginType && (
                    <React.Suspense
                        fallback={<div className="flex-1 overflow-overlay" />}
                    >
                        <UserOrLogin
                            isChatModeEnabled={
                                getFeatureFlagByName(
                                    [
                                        FeatureFlagName.STUDENT_CHAT_MODE,
                                        FeatureFlagName.STUDENT_EXAM_MODE
                                    ],
                                    chatModeExamModeFlag
                                )!
                            }
                        />
                    </React.Suspense>
                )}
                {!loginType && (
                    <Select dir={dir}>
                        <SelectTrigger
                            className={`w-[240px] [&>svg]:stroke-[3.5] [&>svg]:text-white [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotCyan bg-dinoBotCyan ${dir === 'rtl' ? '[&>svg]:rotate-180' : ''}`}
                        >
                            <SelectValue
                                placeholder={
                                    <div className="flex items-center gap-2">
                                        <Image
                                            src={IconDocDetails}
                                            alt="icon doc details"
                                            width={20}
                                            height={20}
                                            className="w-5 h-5"
                                        />
                                        <span>{t('exercise')}</span>
                                    </div>
                                }
                            />
                        </SelectTrigger>
                    </Select>
                )}
            </div>
            <div
                className={`w-fit gap-2 md:h-auto flex flex-col md:flex-row justify-start md:justify-center  items-center md:items-start col-span-3 col-start-3 ${session?.user ? '' : 'ml-2'}`}
            >
                {loginType && <SelectTopic user={user!} />}
            </div>

            <div className="flex flex-row justify-between items-center col-span-3 col-start-6">
                <div className="sm:w-fit lg:h-auto flex flex-col justify-center items-end">
                    {getFeatureFlagByName(
                        [
                            FeatureFlagName.STUDENT_CHAT_MODE,
                            FeatureFlagName.STUDENT_EXAM_MODE
                        ],
                        chatModeExamModeFlag
                    ) ? (
                        <React.Suspense
                            fallback={
                                <div className="flex-1 overflow-overlay" />
                            }
                        >
                            <FilesSideBar />
                        </React.Suspense>
                    ) : null}
                </div>
                {loginType &&
                    (avatarModeEnabled && session?.user ? (
                        <SwitchAvatar />
                    ) : null)}
                <SplitScreen />
                <SelectInlt />
                {session?.user ? (
                    <form
                        //className='flex flex-col justify-center items-end'
                        action={async () => {
                            'use server'
                            await signOut()
                            revalidatePath('/')
                        }}
                    >
                        <DisconnectButton />
                    </form>
                ) : null}
            </div>
        </header>
    )
}
