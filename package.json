{"version": "0.1.229", "private": true, "scripts": {"build": "next build", "coverage": "vitest run --coverage", "dev": "next dev --turbopack", "dev:loop": "zsh run_next_dev.sh", "format:check": "prettier --check \"{app,lib,components}**/*.{ts,tsx,mdx}\" --cache", "format:write": "prettier --write \"{app,lib,components}/**/*.{ts,tsx,mdx}\" --cache", "postinstall": "sh scripts/postinstall.sh", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "seed": "node -r dotenv/config ./scripts/seed.mjs", "start": "next start", "test": "vitest run", "test:ui": "vitest --ui", "test:w": "vitest", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.16", "@ai-sdk/react": "^1.2.9", "@ai-sdk/xai": "^1.2.16", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/client-sns": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@heygen/streaming-avatar": "^2.0.13", "@hookform/resolvers": "^5.0.1", "@next/third-parties": "15.3.0", "@nextui-org/react": "^2.6.11", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/sdk-logs": "^0.200.0", "@phosphor-icons/react": "^2.1.7", "@pixi/graphics": "^7.4.3", "@pixi/react": "^8.0.1", "@prisma/adapter-pg": "^6.6.0", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-primitive": "^2.0.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@react-email/button": "0.0.19", "@react-email/components": "^0.0.41", "@react-email/html": "0.0.11", "@react-email/render": "^1.0.6", "@react-pdf/renderer": "^4.3.0", "@slack/web-api": "^7.9.1", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.1.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "@types/jest": "^29.5.14", "@types/katex": "^0.16.7", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.13", "@types/unzipper": "^0.10.11", "@vercel/otel": "^1.11.0", "@vercel/postgres": "^0.10.0", "ai": "^4.3.9", "axios": "^1.8.4", "better-react-mathjax": "^2.3.0", "child_process": "^1.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "d3-scale": "^4.0.2", "date-fns": "^4.1.0", "desmos": "^1.5.4", "file-type": "^20.4.1", "focus-trap-react": "^11.0.3", "framer-motion": "^12.7.4", "geist": "^1.3.1", "globals": "^16.2.0", "handlebars": "^4.7.8", "katex": "^0.16.22", "langfuse-vercel": "^3.37.2", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "mathlive": "^0.105.0", "mime": "^4.0.7", "moment": "^2.30.1", "motion": "^12.7.4", "nanoid": "^5.1.5", "next": "15.3.1", "next-auth": "5.0.0-beta.25", "next-client-cookies": "^2.0.1", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "node-unrar-js": "^2.0.2", "nodemailer": "^6.10.1", "nuqs": "^2.4.3", "openai": "^4.95.0", "pdf2pic": "^3.1.4", "pg": "^8.14.1", "pino": "^9.6.0", "pino-caller": "^4.0.0", "pino-pretty": "^13.0.0", "pixi.js": "^8.9.1", "quill": "2.0.3", "rc-progress": "^4.0.0", "rc-upload": "^4.9.0", "react": "19.1.0", "react-audio-player-component": "^1.2.4", "react-big-calendar": "^1.18.0", "react-day-picker": "9.7.0", "react-dom": "19.1.0", "react-easy-crop": "^5.4.1", "react-email": "4.0.7", "react-hook-form": "^7.55.0", "react-html-parser": "^2.0.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-quill-new": "^3.4.6", "react-select": "^5.10.1", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "react-timer-hook": "^4.0.5", "react-to-pdf": "^2.0.0", "react-to-print": "^3.0.6", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "rtl-detect": "^1.1.2", "sharp": "^0.34.1", "sonner": "^2.0.3", "spawn-sync": "^2.0.0", "stripe": "^18.0.0", "ts-jest": "^29.3.2", "unzipper": "^0.12.3", "usehooks-ts": "^3.1.1", "xlsx": "^0.18.5", "zod": "^3.25.56", "zod-prisma-types": "^3.2.4", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@next/eslint-plugin-next": "^15.3.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.79.0", "@testcontainers/postgresql": "^10.24.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/d3-scale": "^4.0.9", "@types/desmos": "^1.11.0", "@types/formidable": "^3.4.5", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/react": "19.1.2", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "19.1.2", "@types/react-html-parser": "^2.0.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/rtl-detect": "^1.0.3", "@typescript-eslint/parser": "^8.30.1", "@vitest/coverage-v8": "3.1.1", "@vitest/ui": "^3.1.1", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-tailwindcss": "^3.18.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prisma": "^6.6.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vitest": "^3.1.1"}, "pnpm": {"overrides": {"@types/react": "19.1.2", "@types/react-dom": "19.1.2"}}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "schema": "prisma/schema"}}