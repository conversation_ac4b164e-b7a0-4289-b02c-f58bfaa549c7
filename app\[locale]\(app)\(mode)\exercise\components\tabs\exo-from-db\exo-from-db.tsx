import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import React, { useEffect, useState } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'

import InfoTooltip from '@/components/ui/info-tooltip'
import { Chapter, Part } from '@/lib/training-mode/types'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import useExoModeStore, {
    selectUseExoModeStore
} from '@/lib/stores/exercise-mode-store/exercise-store'
import { toast } from 'sonner'
import ErrorTooltip from '@/components/ui/error-tooltip'
import { usePathname, useRouter } from '@/i18n/routing'
import { useLocale, useTranslations } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'
import { Domain } from '@prisma/client'
import useAccountStore from '@/lib/stores/account-store/store'
import { MultiSelect } from '@/components/ui/multi-select'
import { join } from 'path'
import { useCookies } from 'next-client-cookies'

interface ExoFromDbProps {
    domains: Domain[]
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
}

function ExoFromDb({ domains, getChapters, getParts }: ExoFromDbProps) {
    const formData = selectUseExoModeStore.use.exoInfoFromDb()
    const cookies = useCookies()
    const [selectedChapter, setSelectedChapter] = useState<string | undefined>(
        formData.chapterId
    )
    const path = usePathname()
    const [parts, setParts] = useState<Part[]>([])
    const [chapters, setChapters] = useState<Chapter[]>([])
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromDb()
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = useExoModeStore(state => state.setMode)
    const t = useTranslations('app.mode.exo.tab.db')
    const tMultiSelect = useTranslations('components.multiselect')
    const router = useRouter()
    const lang = useLocale()
    const { user } = useAccountStore()
    const levelIdFromCookie = cookies.get('levelId')

    useEffect(() => {
        setMode('FROM_DB')
    }, [])

    useEffect(() => {
        if (
            formData.domainName &&
            formData.domainName.length > 0 &&
            levelIdFromCookie
        ) {
            ;(async () => {
                const chaps = await getChapters(
                    formData.domainName,
                    levelIdFromCookie!
                )
                setChapters(chaps)
            })()
        } else {
            setChapters([])
        }
    }, [formData.domainName, levelIdFromCookie])

    useEffect(() => {
        if (formData.chapterId) {
            setSelectedChapter(formData.chapterId)
            ;(async () => {
                const parts = await getParts(formData.chapterId)
                setParts(parts)
            })()
        } else {
            setSelectedChapter(undefined)
            setParts([])
        }
    }, [formData.chapterId])

    useEffect(() => {
        if (selectedChapter) {
            handleFormChange('chapterId', selectedChapter)
        }
    }, [selectedChapter])

    const normalizeExerciseNumber = (value: number): number => {
        if (value > 10) return 10
        if (value < 1) return 1
        return value
    }

    const showLimitInfo = (value: number) => {
        if (value > 10) {
            toast.info(t('tinfo.n10'), { duration: 2500 })
        } else if (value < 1) {
            toast.info(t('tinfo.n1'), { duration: 2500 })
        }
    }

    const handleNumberChange = (
        field: 'qstNbr' | 'exoNbr',
        value: number,
        defaultValue: number
    ) => {
        try {
            const normalizedValue = normalizeExerciseNumber(value)
            showLimitInfo(value)
            handleFormChange(field, normalizedValue)
        } catch (error) {
            console.log(error)
            handleFormChange(field, defaultValue)
        }
    }

    const handleQuestionNumberChange = (value: number) => {
        handleNumberChange('qstNbr', value, 5)
    }

    const handleExerciseNumberChange = (value: number) => {
        handleNumberChange('exoNbr', value, 3)
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    useEffect(() => {
        if (formData.exoNbr) handleExerciseNumberChange(formData.exoNbr)
    }, [formData.exoNbr])

    const submit = () => {
        if (formData.chapterId && formData.partIds.length > 0) {
            if (formData.qstNbr && formData.exoNbr) {
                setShowError(false)
                setMode('FROM_DB')
                router.push(`/${path}/train`)
            } else {
                toast.error(t('tinfo.error'))
            }
        } else {
            setShowError(true)
            toast.info(t('tinfo.info'))
        }
    }

    return (
        <div className="flex flex-col gap-4 w-full">
            <div className="flex flex-col gap-y-9">
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('subject.name')}{' '}
                    </div>
                    <Select
                        value={formData.domainName}
                        onValueChange={value => {
                            handleFormChange('domainName', value)
                            handleFormChange('chapterId', '') // Reset chapterId when domain changes
                            handleFormChange('partIds', []) // Reset partIds when domain changes
                            setSelectedChapter(undefined) // Reset selectedChapter state
                            const selectedDomain = domains.find(
                                domain => domain.name === value
                            )
                            if (selectedDomain) {
                                cookies.set(
                                    'topicId',
                                    selectedDomain.id.toString()
                                )
                                cookies.set('topic', selectedDomain.name)
                            }
                        }}
                    >
                        <SelectTrigger className="max-w-full">
                            <SelectValue
                                placeholder={t('subject.placeholder')}
                            />
                        </SelectTrigger>
                        <SelectContent
                            className={`${domains.length > 5 ? 'h-48' : 'h-fit'}`}
                        >
                            <SelectGroup>
                                {domains.map(domain => (
                                    <SelectItem
                                        key={domain.id}
                                        value={domain.name}
                                    >
                                        {getLangProps({
                                            obj: domain,
                                            base: 'name',
                                            lang
                                        })}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('chois.title')}{' '}
                    </div>
                    <Select
                        value={formData.chapterId}
                        onValueChange={value => {
                            handleFormChange('partIds', [])
                            setSelectedChapter(value)
                        }}
                        disabled={chapters.length <= 0}
                    >
                        <SelectTrigger className="max-w-full">
                            <SelectValue placeholder={t('chois.placeholder')} />
                        </SelectTrigger>
                        <SelectContent
                            className={`${chapters.length > 5 ? 'h-48' : 'h-fit'}`}
                        >
                            <SelectGroup>
                                {chapters.map(chapter => (
                                    <SelectItem
                                        key={chapter.id}
                                        value={chapter.id}
                                    >
                                        {getLangProps({
                                            obj: chapter,
                                            base: 'title',
                                            lang
                                        })}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('part.title')}{' '}
                    </div>
                    <MultiSelect
                        options={parts.map(part => ({
                            label: getLangProps({
                                obj: part,
                                base: 'name',
                                lang
                            }),
                            value: part.id
                        }))}
                        onValueChange={values =>
                            handleFormChange('partIds', values)
                        }
                        value={formData.partIds}
                        disabled={parts.length <= 0}
                        placeholder={t('part.placeholder')}
                        badgeclassName="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/80 text-white"
                        translations={{
                            selectAll: tMultiSelect('selectAll'),
                            search: tMultiSelect('search'),
                            noResults: tMultiSelect('noResults'),
                            clear: tMultiSelect('clear'),
                            close: tMultiSelect('close'),
                            more: tMultiSelect('more')
                        }}
                    />
                </div>

                <div className="w-full flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('exonumber')}
                    </div>
                    <Input
                        type="number"
                        value={formData.exoNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-md"
                        onChange={e =>
                            handleFormChange(
                                'exoNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>
                <div className="w-full flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('questions-number')}
                    </div>
                    <Input
                        type="number"
                        value={formData.qstNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-md"
                        onChange={e =>
                            handleFormChange(
                                'qstNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>

                <div className="w-full sm:w-4/5 md:w-3/5">
                    <div className="flex gap-5 items-center text-sm font-bold text-dinoBotDarkGray">
                        <span>{t('difficulty')}</span>
                        <div className="flex-1">
                            <Slider
                                defaultValue={[formData.difficulty]}
                                min={0}
                                max={3}
                                step={1}
                                className="w-full"
                                onValueChange={value =>
                                    handleFormChange('difficulty', value[0])
                                }
                            />
                        </div>
                        <span>{formData.difficulty}/3</span>
                    </div>
                </div>

                <div className="flex flex-col gap-1">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {' '}
                        {t('custom-prompt')}{' '}
                        <span className="font-normal">{t('opt')}</span>{' '}
                    </div>
                    <div className="mt-2">
                        <Textarea
                            placeholder={t('redigez')}
                            value={formData.customPrompt}
                            onChange={e => {
                                if (e.target.value.length <= 1000)
                                    handleFormChange(
                                        'customPrompt',
                                        e.target.value
                                    )
                            }}
                        ></Textarea>
                        <p className="text-xs text-right mt-1 text-dinoBotGray">
                            {formData.customPrompt.length ?? 0}/1000{' '}
                            {t('caracteres')}
                        </p>
                    </div>
                </div>
            </div>
            <div className="w-full flex justify-center items-center mt-2">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromDb
