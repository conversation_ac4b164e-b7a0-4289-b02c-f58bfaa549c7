'use client'
import { cn } from '@/lib/utils/utils'
import { EmptyScreen } from '@/components/empty-screen'
//import { ChatScrollAnchor } from '@/components/chat-scroll-anchor'
import React, { useEffect, useReducer, useRef, useState, useMemo } from 'react'
import { Session } from '@/lib/types'
import { Message } from '@/lib/chat/actions'
import { toast } from 'sonner'
import { useScrollAnchor } from '@/lib/hooks/use-scroll-anchor'
import {
    Exo,
    ExoOutput,
    ExoSortsAndFilters,
    ExoSubject
} from '@/lib/exams/actions'
import { TrainingChatPanel } from './training-chat-panel'
import {
    TrainingModeGeneratorInput,
    TrainingModeGeneratorOutput
} from '@/lib/training/actions'
import useExoModeStore, {
    ExoInfoFromDb,
    ExoInfoFromExam,
    ExoInfoFromFile,
    selectUseExoModeStore
} from '@/lib/stores/exercise-mode-store/exercise-store'
import Question<PERSON>iewer from './exo-viewer'
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import {
    Check,
    ChevronLeft,
    Clipboard,
    Download,
    FileText,
    Printer,
    RotateCw,
    X
} from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import generatePDF, { Margin } from 'react-to-pdf'
import Image from 'next/image'
import { Chapter, Part } from '@/lib/training-mode/types'
import TrainingCortexDialog from './training-cortex-dialog'
import { usePathname, useRouter } from '@/i18n/routing'
import { useCookies } from 'next-client-cookies'
import { useLocale, useTranslations } from 'next-intl'
import { DesmosUi } from '@/components/desmos/DesmosUi'
import { getLangDir } from 'rtl-detect'
import { messageFileType } from '../../../../(chat)/components/chat'
import { ChatRequestOptions } from 'ai'
import {
    useCurrentViewStore,
    VIEWS
} from '@/app/[locale]/(app)/(mode)/store/current-view-store'

export interface TrainChatProps extends React.ComponentProps<'div'> {
    initialMessages?: Message[]
    id?: string
    session?: Session
    missingKeys: string[]
    generateExo: (
        input: TrainingModeGeneratorInput
    ) => Promise<TrainingModeGeneratorOutput[] | null>
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
    getExams: (filters: ExoSortsAndFilters) => Promise<ExoOutput[]>
    getSubjects: (domainName: string) => Promise<ExoSubject[]>
    getExo: (id: string) => Promise<Exo>
}

// Définir le type d'état
interface TrainingChatState {
    chapters: Chapter[]
    subjects: ExoSubject[]
    pdf: any
    pdfIsOpen: boolean
    exoLoading: boolean
    regeneratingExerciseIndex: number | null
    imageFile: string
    fileExtension: string
    previousPath: string
    messagesFileMap: Map<number, messageFileType>
}

export function TrainChat({
    className,
    session,
    missingKeys,
    generateExo,
    getChapters,
    getParts,
    getExams,
    getSubjects,
    getExo
}: TrainChatProps) {
    // État initial
    const initialState: TrainingChatState = {
        chapters: [],
        subjects: [],
        pdf: null,
        pdfIsOpen: false,
        exoLoading: false,
        regeneratingExerciseIndex: null,
        imageFile: '',
        fileExtension: '',
        previousPath: '',
        messagesFileMap: new Map<number, messageFileType>()
    }

    // Utiliser le format simplifié de useReducer
    const [state, dispatch] = useReducer(
        (state: TrainingChatState, change: Partial<TrainingChatState>) => ({
            ...state,
            ...change
        }),
        initialState
    )

    // Destructurer l'état pour faciliter l'accès
    const {
        chapters,
        subjects,
        pdf,
        pdfIsOpen,
        exoLoading,
        regeneratingExerciseIndex,
        imageFile,
        fileExtension,
        previousPath,
        messagesFileMap
    } = state
    const examId = useExoModeStore(state => state.exoInfoFromExam.examId)
    const { currentView } = useCurrentViewStore()
    const [domains, setDomains] = useState<Domain[]>([])

    const openAssignementPDF = async () => {
        const exo = await getExo(examId)
        dispatch({
            pdf: {
                name: `${exo.title} - énoncé`,
                type: 'pdf',
                data: `data:application/pdf;base64,${exo.assignmentMedia}`
            },
            pdfIsOpen: false
        })
    }

    useEffect(() => {
        if (examId && mode === 'FROM_EXAM') {
            ;(async () => {
                await openAssignementPDF()
            })()
        } else {
            dispatch({ pdf: null, pdfIsOpen: false })
        }
    }, [examId])

    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()
    const router = useRouter()
    const path = usePathname()
    const locale = useLocale()
    const exo = selectUseExoModeStore.use.currentGExo()
    const subject = selectUseExoModeStore.use.subject()
    const level = selectUseExoModeStore.use.level()
    const cookies = useCookies()
    const levelIdFromCookie = cookies.get('levelId')
    const { user } = useAccountStore()
    const setExo = selectUseExoModeStore.use.setCurrentGExo()
    // const exercise = selectUseExoModeStore.use.
    const t = useTranslations('app.mode.train')
    const exosRef = useRef(null)

    // Create individual refs for each exercise
    const individualExoRefs = useMemo(() => {
        return exo.map(() => React.createRef<HTMLDivElement>())
    }, [exo.length])

    const mode = selectUseExoModeStore.use.mode()
    const exoInfo =
        mode === 'FROM_FILE'
            ? selectUseExoModeStore.use.exoInfoFromFile()
            : mode === 'FROM_EXAM'
              ? selectUseExoModeStore.use.exoInfoFromExam()
              : selectUseExoModeStore.use.exoInfoFromDb()
    const {
        messages,
        input,
        handleInputChange,
        handleSubmit,
        status,
        setMessages,
        error
    } = useChat({
        initialMessages: [],
        api: locale ? `/api/chat?locale=${locale}` : '/api/chat',
        body: {
            imageFile,
            fileExtension,
            exercise: exo
        }
    })
    const resetState = useExoModeStore(state => state.reset)

    const topic = Number(cookies.get('topicId'))
    const { data: domain } = useQuery({
        queryKey: ['domain', topic],
        queryFn: () => getDomainById(topic),
        enabled: !!topic,
        select: data => {
            if ('error' in data) return
            return data
        }
    })
    // const [_, setNewChatId] = useLocalStorage('newChatId', id)
    const regenerateNewExo = async () => {
        dispatch({ exoLoading: true })
        dispatch({ pdfIsOpen: false })
        const ex = await generateExo({
            difficulty: exoInfo.difficulty,
            numberOfExercises: exoInfo.exoNbr,
            numberOfQuestions: exoInfo.qstNbr,
            variation: exoInfo.variation,
            customPrompt: exoInfo.customPrompt,
            lastResult: `- ${exo.map(x => x.questionContent).join('/n - ')}`,
            level: level!,
            domain: domain!
        })
        if (ex) setExo(ex)
        dispatch({ exoLoading: false })
    }

    const generateNewExo = async () => {
        dispatch({ exoLoading: true })
        let exercice: TrainingModeGeneratorOutput[] | null = null

        try {
            switch (mode) {
                case 'FROM_DB':
                    exercice = await generateExo({
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: exoInfo.exoNbr,
                        numberOfQuestions: exoInfo.qstNbr,
                        partIds: (exoInfo as ExoInfoFromDb).partIds,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        level: level!,
                        domain: domain!
                    })
                    break
                case 'FROM_FILE':
                    exercice = await generateExo({
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: exoInfo.exoNbr,
                        numberOfQuestions: exoInfo.qstNbr,
                        file: (exoInfo as ExoInfoFromFile).exo,
                        solutionFile: (exoInfo as ExoInfoFromFile).solution,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        level: level!,
                        domain: domain!
                    })
                    break
                case 'FROM_EXAM':
                    exercice = await generateExo({
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: exoInfo.exoNbr,
                        numberOfQuestions: exoInfo.qstNbr,
                        exoId: (exoInfo as ExoInfoFromExam).examId,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        level: level!,
                        domain: domain!
                    })
                    break
                default:
                    router.back()
                    break
            }

            if (exercice) {
                setKnowledgeBaseModeContent(
                    `voicie l'exercice ${JSON.stringify(exercice)}\n et le chat ${JSON.stringify(messages)}`
                )
                setExo(exercice)
            }
        } catch (error) {
            console.error('Error generating exercise:', error)
            toast.error(t('error'))
            router.back()
        } finally {
            dispatch({ exoLoading: false, pdf: null, pdfIsOpen: false })
        }
    }

    // Track if this is the first visit to prevent regeneration on domain changes
    const [isFirstVisit, setIsFirstVisit] = useState(true)

    useEffect(() => {
        ;(async () => {
            if (levelIdFromCookie && domain) {
                ;(async () => {
                    const domains = await getDomainsByLevel(levelIdFromCookie!)
                    if ('error' in domains) {
                        //toast.error('Errore fetching domains: ' + domains.error)
                        return // Exit if there's an error
                    }
                    setDomains(domains)
                })()

                // Only generate new exercise on first visit
                if (isFirstVisit) {
                    await generateNewExo()
                    setIsFirstVisit(false)
                }

                const chaps = await getChapters(
                    cookies.get('topic') as string,
                    levelIdFromCookie
                )
                dispatch({ chapters: chaps })
            }
            if (subject) {
                const subs = await getSubjects(subject)
                dispatch({ subjects: subs })
            }
            dispatch({ pdf: null, pdfIsOpen: false })
        })()
    }, [cookies, levelIdFromCookie, domain])

    useEffect(() => {
        if (previousPath.includes('chat') && path === '/') {
            setMessages([])
            router.refresh()
        }
        dispatch({ previousPath: path })
    }, [path])

    useEffect(() => {
        missingKeys.map(key => {
            toast.error(`${t('env.part1')} ${key} ${t('env.part2')}`)
        })
    }, [missingKeys])

    const {
        messagesRef,
        scrollRef,
        visibilityRef,
        isAtBottom,
        scrollToBottom
    } = useScrollAnchor()

    const copyExo = () => {
        let text = ''
        exo.map((x, index) => {
            text += `${index + 1}. ${x.questionContent}\n`
        })
        navigator.clipboard.writeText(text)
    }

    // Individual exercise functions
    const copyExercise = (
        exercise: TrainingModeGeneratorOutput,
        index: number
    ) => {
        const text = `${index + 1}. ${exercise.questionContent}`
        navigator.clipboard.writeText(text)
    }

    // Individual regenerate function
    const regenerateExercise = async (index: number) => {
        dispatch({ regeneratingExerciseIndex: index })
        try {
            let ex: TrainingModeGeneratorOutput[] | null = null

            switch (mode) {
                case 'FROM_DB':
                    ex = await generateExo({
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: 1,
                        numberOfQuestions: 1,
                        partIds: (exoInfo as ExoInfoFromDb).partIds,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        lastResult: `- ${exo[index].questionContent}`,
                        level: level!,
                        domain: domain!
                    })
                    break
                case 'FROM_FILE':
                    ex = await generateExo({
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: 1,
                        numberOfQuestions: 1,
                        file: (exoInfo as ExoInfoFromFile).exo,
                        solutionFile: (exoInfo as ExoInfoFromFile).solution,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        lastResult: `- ${exo[index].questionContent}`,
                        level: level!,
                        domain: domain!
                    })
                    break
                case 'FROM_EXAM':
                    ex = await generateExo({
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: 1,
                        numberOfQuestions: 1,
                        exoId: (exoInfo as ExoInfoFromExam).examId,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        lastResult: `- ${exo[index].questionContent}`,
                        level: level!,
                        domain: domain!
                    })
                    break
            }

            if (ex && ex.length > 0) {
                // Update only the specific exercise in the array
                const newExo = [...exo]
                newExo[index] = ex[0]
                setExo(newExo)
            }
        } catch (error) {
            console.error('Error regenerating exercise:', error)
            toast.error(t('error'))
        } finally {
            dispatch({ regeneratingExerciseIndex: null })
        }
    }

    // Individual print functions using the same mechanism as printExos
    const printExercise = (index: number) => {
        if (individualExoRefs[index]?.current) {
            // Create a temporary print function
            const element = individualExoRefs[index].current
            if (element) {
                const printWindow = window.open('', '_blank')
                if (printWindow) {
                    printWindow.document.write(`
                        <html>
                            <head>
                                <title>DinoBot Exercise ${index + 1}</title>
                                <style>
                                    body { font-family: Arial, sans-serif; margin: 20px; }
                                    .exercise-header { text-align: center; margin-bottom: 20px; }
                                    .exercise-content { margin: 20px 0; }
                                </style>
                            </head>
                            <body>
                                ${element.innerHTML}
                            </body>
                        </html>
                    `)
                    printWindow.document.close()
                    printWindow.print()
                    printWindow.close()
                }
            }
        }
    }

    const downloadExerciseAsPdf = async (index: number) => {
        if (individualExoRefs[index]?.current) {
            await generatePDF(individualExoRefs[index], {
                filename: `dinobot-exercise-${index + 1}.pdf`,
                page: { margin: Margin.SMALL }
            })
        }
    }

    const printExos = useReactToPrint({
        contentRef: exosRef
    })

    const reset = () => {
        resetState()
        router.back()
    }

    const downloadAsPdf = async () => {
        await generatePDF(exosRef, {
            filename: 'dinobot-exercise.pdf',
            page: { margin: Margin.SMALL }
        })
    }

    const GenerateMinTab = () => {
        switch (mode) {
            case 'FROM_DB':
                return (
                    <ExoFromDbMin
                        domains={domains}
                        chapters={chapters}
                        getChapters={getChapters}
                        getParts={getParts}
                        onGenerate={generateNewExo}
                    />
                )
            case 'FROM_FILE':
                return <ExoFromFileMin onGenerate={generateNewExo} />
            case 'FROM_EXAM':
                return (
                    <ExoFromExamMin
                        domains={domains}
                        getExams={getExams}
                        subjects={subjects}
                        onGenerate={generateNewExo}
                    />
                )
        }
    }

    const handleSubmitWithFile = (
        e?: { preventDefault?: () => void },
        chatRequestOptions?: ChatRequestOptions
    ) => {
        e?.preventDefault?.()

        // Si un fichier est présent, assurez-vous qu'il est inclus dans la requête
        if (imageFile) {
            // useChat va automatiquement utiliser le body configuré ci-dessus
            handleSubmit(e, chatRequestOptions)

            // Réinitialiser le fichier après l'envoi
            dispatch({ imageFile: '', fileExtension: '' })
        } else {
            // Envoi normal sans fichier
            handleSubmit(e, chatRequestOptions)
        }
    }

    // Fonction pour ajouter un fichier à la Map
    const addFileData = (data: messageFileType) => {
        const newMap = new Map(messagesFileMap)
        newMap.set(messages.length, data)
        dispatch({ messagesFileMap: newMap })
    }

    return (
        <div className="bg-white relative size-full flex flex-col md:flex-row justify-center items-center overflow-hidden ">
            <CheckMessageTraning
                messages={messages}
                setMessages={setMessages}
                session={session}
            />
            {(currentView === VIEWS.SPLIT_SCREEN ||
                currentView === VIEWS.EXERCISE) && (
                <div
                    className={cn(
                        'w-full pt-4 relative h-2/5 md:h-full md:py-3 lg:px-8 2xl:pl-16 2xl:pr-8 overflow-y-auto overflow-x-hidden',
                        currentView === VIEWS.SPLIT_SCREEN &&
                            'md:w-1/2 border-b md:border-r border-dinoBotLightGray',
                        currentView === VIEWS.EXERCISE && 'md:w-1/2'
                    )}
                >
                    <button
                        onClick={reset}
                        className="flex justify-center items-center top-5 lg:mt-4 lg:left-3 left-5 xl:left-12 absolute size-8 bg-dinoBotBlue hover:bg-dinoBotBlue/80 text-white rounded-full"
                    >
                        <ChevronLeft className="size-6 mr-0.5" />
                    </button>
                    <div className="size-full flex flex-col px-2 xl:px-8  sm:mt-8 lg:mt-0">
                        {!exoLoading ? (
                            <div className="flex flex-col gap-3 p-4 xl:px-8">
                                <div className="absolute top-[-9999px] ">
                                    <div ref={exosRef}>
                                        <ExoPrintView exo={exo} />
                                    </div>
                                    {/* Individual exercise print views */}
                                    {exo.map((exercise, index) => (
                                        <div
                                            key={`print-${index}`}
                                            ref={individualExoRefs[index]}
                                        >
                                            <SingleExoPrintView
                                                exercise={exercise}
                                                index={index}
                                            />
                                        </div>
                                    ))}
                                </div>
                                <div className="p-4 bg-dinoBotDarkGray/5 rounded-md">
                                    {GenerateMinTab()}
                                </div>
                                <div className="w-full flex justify-between py-1 border-b border-dinoBotBlue">
                                    <div className="text-lg font-extrabold text-dinoBotBlue">
                                        {/* {t('exo')} */}
                                    </div>
                                    <div>
                                        <ExoOptions
                                            onCopy={copyExo}
                                            onGenerate={regenerateNewExo}
                                            onPrint={printExos}
                                            onDownload={async () => {
                                                await downloadAsPdf()
                                            }}
                                        />
                                    </div>
                                </div>
                                <div className="w-full pl-2 list-disc mb-4">
                                    {exo &&
                                        exo?.length > 0 &&
                                        exo?.map((x, index) => (
                                            <div
                                                key={index}
                                                className="mb-6 border-b border-gray-200 pb-4 last:border-b-0"
                                            >
                                                <div className="flex justify-between items-center mb-2">
                                                    <p className="font-bold">
                                                        {t('exo')} {index + 1}:
                                                    </p>
                                                    <ExoOptions
                                                        onCopy={() =>
                                                            copyExercise(
                                                                x,
                                                                index
                                                            )
                                                        }
                                                        onGenerate={() =>
                                                            regenerateExercise(
                                                                index
                                                            )
                                                        }
                                                        onPrint={() =>
                                                            printExercise(index)
                                                        }
                                                        onDownload={() =>
                                                            downloadExerciseAsPdf(
                                                                index
                                                            )
                                                        }
                                                    />
                                                </div>
                                                {regeneratingExerciseIndex ===
                                                index ? (
                                                    <ExoSkeleton />
                                                ) : (
                                                    <div className="animate-fade-in-down">
                                                        <QuestionViewer
                                                            value={
                                                                x.questionContent
                                                            }
                                                        />
                                                        {x.desmosCode &&
                                                        x.desmosCode
                                                            .expressions ? (
                                                            <DesmosUi
                                                                data={
                                                                    x.desmosCode
                                                                }
                                                                className="w-96 h-80"
                                                            />
                                                        ) : (
                                                            ''
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                </div>
                            </div>
                        ) : (
                            <ExoLoading />
                        )}
                        {mode === 'FROM_EXAM' && (
                            <ExamPdfViewer
                                show={pdfIsOpen}
                                pdf={pdf}
                                onClose={() => dispatch({ pdfIsOpen: false })}
                                onOpen={() => dispatch({ pdfIsOpen: true })}
                            />
                        )}
                    </div>
                </div>
            )}
            {(currentView === VIEWS.SPLIT_SCREEN ||
                currentView === VIEWS.CHAT) && (
                <div
                    className={cn(
                        'relative group w-full h-3/5 md:h-full overflow-auto pl-0',
                        currentView === VIEWS.SPLIT_SCREEN &&
                            'md:w-1/2 border-l border-l-dinoBotLightGray',
                        currentView === VIEWS.CHAT && 'md:w-2/3'
                    )}
                    ref={scrollRef}
                >
                    <div
                        className={cn(
                            'pb-[200px] mt-4 pt-4 md:pt-2',
                            className
                        )}
                        ref={messagesRef}
                    >
                        <div
                            className={` transition-all duration-300 px-8 2xl:px-0`}
                        >
                            {messages.length ? (
                                <>
                                    <ChatList
                                        messages={messages}
                                        exercise={undefined}
                                        status={status}
                                        error={error}
                                        messagesFileMap={messagesFileMap}
                                    />
                                </>
                            ) : (
                                // <EmptyScreen session={session!} />
                                <></>
                            )}
                        </div>

                        <div className="h-px w-full" ref={visibilityRef} />
                    </div>
                    <div
                        className={cn(
                            'w-full fixed bottom-8',
                            currentView === VIEWS.SPLIT_SCREEN && 'md:w-[50vw]',
                            currentView === VIEWS.CHAT &&
                                'md:w-full left-0 right-0'
                        )}
                    >
                        {messages.length ? (
                            <></>
                        ) : (
                            <EmptyScreen session={session!} />
                        )}
                        <TrainingChatPanel
                            input={input}
                            setInput={handleInputChange}
                            imageFile={imageFile}
                            setImageFile={value =>
                                dispatch({ imageFile: value })
                            }
                            fileExtension={fileExtension}
                            setFileExtension={value =>
                                dispatch({ fileExtension: value })
                            }
                            isAtBottom={isAtBottom}
                            scrollToBottom={scrollToBottom}
                            session={session}
                            status={status}
                            addFileData={addFileData}
                            handleSubmit={handleSubmitWithFile}
                        />
                    </div>
                    <TrainingCortexDialog />
                </div>
            )}
        </div>
    )
}

interface ExoOptionsProps {
    onCopy?: () => void
    onGenerate?: () => void
    onPrint?: () => void
    onDownload?: () => void
}

function ExoOptions({
    onCopy,
    onGenerate,
    onPrint,
    onDownload
}: ExoOptionsProps) {
    const [animGenerate, setAnimGenerate] = useState(false)
    const [animCopy, setAnimCopy] = useState(false)
    const [animDownload, setAnimDownload] = useState(false)
    const t = useTranslations('app.mode.train')
    useEffect(() => {
        if (animGenerate) setTimeout(() => setAnimGenerate(false), 1500)
        if (animCopy) setTimeout(() => setAnimCopy(false), 1500)
        if (animDownload) setTimeout(() => setAnimDownload(false), 1500)
    }, [animGenerate, animCopy, animDownload])

    return (
        <div className="flex gap-2">
            {/* <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="ghost" className="size-5 p-0 hover:bg-background">
            <Volume2 />
            <span className="sr-only">Lire L&apos;exercice</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent className="bg-dinoBotBlue">
          Lire l&apos;exercice
        </TooltipContent>
      </Tooltip> */}

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onCopy
                                ? () => {
                                      setAnimCopy(true)
                                      onCopy()
                                  }
                                : undefined
                        }
                    >
                        {!animCopy ? (
                            <Clipboard />
                        ) : (
                            <Check className="animate-fade-in" />
                        )}
                        <span className="sr-only">{t('copyexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('copyexo')}
                </TooltipContent>
            </Tooltip>

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onGenerate
                                ? () => {
                                      setAnimGenerate(true)
                                      onGenerate()
                                  }
                                : undefined
                        }
                    >
                        <RotateCw
                            className={`${animGenerate ? 'animate-spin' : ''}`}
                        />
                        <span className="sr-only">{t('regexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('regexo')}
                </TooltipContent>
            </Tooltip>

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onDownload
                                ? () => {
                                      setAnimDownload(true)
                                      onDownload()
                                  }
                                : undefined
                        }
                    >
                        {!animDownload ? (
                            <Download />
                        ) : (
                            <Check className="animate-fade-in" />
                        )}
                        <span className="sr-only">{t('downloadexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('downloadexo')}
                </TooltipContent>
            </Tooltip>

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={onPrint ?? undefined}
                    >
                        <Printer />
                        <span className="sr-only">{t('printexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('printexo')}
                </TooltipContent>
            </Tooltip>
        </div>
    )
}

function ExoLoading() {
    const t = useTranslations('app.mode.train')
    return (
        <div className="flex flex-col gap-3 p-4 xl:p-10">
            <div className="w-full flex justify-between py-1 border-b-1 animate-pulse">
                <div className="w-16 h-3 rounded-3xl bg-slate-400 animate-pulse"></div>
                <div className="w-28 h-3 rounded-3xl bg-slate-400 animate-pulse"></div>
            </div>
            <ul className="w-full">
                {[1, 2, 3, 4, 5].map((x, index) => (
                    <li key={index}>
                        <div className="w-full h-4 rounded-3xl bg-slate-300 my-2 animate-pulse"></div>
                    </li>
                ))}
            </ul>
            <div className="w-full flex justify-center">
                <div className="text-sm text-slate-500 font-semibold animate-pulse">
                    {t('creation')}
                </div>
            </div>
        </div>
    )
}

function ExoSkeleton() {
    const t = useTranslations('app.mode.train')
    return (
        <div className="flex flex-col gap-3 p-2">
            <ul className="w-full">
                {[1, 2, 3].map((x, index) => (
                    <li key={index}>
                        <div className="w-full h-4 rounded-3xl bg-slate-300 my-2 animate-pulse"></div>
                    </li>
                ))}
            </ul>
            <div className="w-full flex justify-center">
                <div className="text-sm text-slate-500 font-semibold animate-pulse">
                    {t('creation')}
                </div>
            </div>
        </div>
    )
}

interface ExoPrintViewProps {
    exo: TrainingModeGeneratorOutput[]
}

interface SingleExoPrintViewProps {
    exercise: TrainingModeGeneratorOutput
    index: number
}

import DinoLogo from '@/public/dinobot-logo-chat.svg'
import useAccountStore from '@/lib/stores/account-store/store'
import { selectUseAvatarStore } from '@/lib/stores/avatar-store/avatar-store'
import ExoFromDbMin from '../../../exercise/components/min-tabs/exo-from-db'
import ExoFromFileMin from '../../../exercise/components/min-tabs/exo-from-file'
import ExoFromExamMin from '../../../exercise/components/min-tabs/exo-from-exam/exo-from-exam'
import MiniPDFReader from '@/components/pdf-reader/pdf-reader-mini'
import { useChat } from '@ai-sdk/react'
import { ChatList } from '../../../../components/chat-list'
import CheckMessageTraning from './check-message-traning'
import { useQuery } from '@tanstack/react-query'
import { getDomainById, getDomainsByLevel } from '@/lib/domains/actions'
import { Domain } from '@prisma/client'
function ExoPrintView({ exo }: ExoPrintViewProps) {
    const t = useTranslations('app.mode.train')
    const locale = useLocale()
    const dir = getLangDir(locale)
    return (
        <div dir={dir}>
            <div className="w-full flex gap-2 items-center justify-center py-3 bg-dinoBotVibrantBlue/5">
                <Image src={DinoLogo} alt="logo" width={48} height={48} />
                <div className="text-2xl text-dinoBotBlue font-semibold">
                    DinoBot
                </div>
            </div>
            <div className="flex flex-col gap-3 px-10 py-2">
                <div className="w-full flex justify-between py-1  border-b border-dinoBotBlue">
                    <div className="text-lg font-extrabold text-dinoBotBlue">
                        {t('exo')}
                    </div>
                    <div></div>
                </div>
                <ul className="w-full pl-4 list-disc ml-4 ">
                    {exo &&
                        exo?.length > 0 &&
                        exo?.map((x, index) => (
                            <li key={index}>
                                <p className="font-bold">
                                    {t('question')} {index + 1}:
                                </p>
                                <div style={{ pageBreakInside: 'avoid' }}>
                                    <QuestionViewer value={x.questionContent} />
                                    {x.desmosCode &&
                                    x.desmosCode.expressions ? (
                                        <DesmosUi
                                            data={x.desmosCode}
                                            className="w-96 h-80"
                                            style={{ pageBreakInside: 'avoid' }}
                                        />
                                    ) : (
                                        ''
                                    )}
                                </div>
                            </li>
                        ))}
                </ul>
            </div>
        </div>
    )
}

function SingleExoPrintView({ exercise, index }: SingleExoPrintViewProps) {
    const t = useTranslations('app.mode.train')
    const locale = useLocale()
    const dir = getLangDir(locale)
    return (
        <div dir={dir}>
            <div className="w-full flex gap-2 items-center justify-center py-3 bg-dinoBotVibrantBlue/5">
                <Image src={DinoLogo} alt="logo" width={48} height={48} />
                <div className="text-2xl text-dinoBotBlue font-semibold">
                    DinoBot
                </div>
            </div>
            <div className="flex flex-col gap-3 px-10 py-2">
                <div className="w-full flex justify-between py-1 border-b border-dinoBotBlue">
                    <div className="text-lg font-extrabold text-dinoBotBlue">
                        {t('exo')} {index + 1}
                    </div>
                    <div></div>
                </div>
                <div className="w-full pl-4">
                    <p className="font-bold">
                        {t('question')} {index + 1}:
                    </p>
                    <div style={{ pageBreakInside: 'avoid' }}>
                        <QuestionViewer value={exercise.questionContent} />
                        {exercise.desmosCode &&
                        exercise.desmosCode.expressions ? (
                            <DesmosUi
                                data={exercise.desmosCode}
                                className="w-96 h-80"
                                style={{ pageBreakInside: 'avoid' }}
                            />
                        ) : (
                            ''
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}

interface PdfViewerProps {
    show: boolean
    pdf: any
    onClose: () => void
    onOpen: () => void
}

function ExamPdfViewer({ show, pdf, onClose, onOpen }: PdfViewerProps) {
    return pdf && show ? (
        <div className="size-full  sm:w-[500px] sm:h-[600px] p-1 fixed bottom-1/2 translate-y-1/2 2xl:left-10 z-50 bg-white custom-scroller border border-dinoBotGray overflow-hidden">
            <div className=" px-2 py-1 w-full flex justify-end items-center">
                <Button
                    variant="ghost"
                    className="p-0 size-7 rounded-full hover:animate-wiggle hover:text-dinoBotRed text-dinoBotRed "
                    onClick={onClose}
                >
                    <X className="size-5" />
                </Button>
            </div>
            <div className="size-full overflow-y-auto overflow-x-hidden  custom-scroller">
                <div className=" flex items-center shadow-md  flex-col justify-start">
                    <div className="overflow-auto">
                        <MiniPDFReader file={pdf} sideViewer={true} />
                    </div>
                </div>
            </div>
        </div>
    ) : pdf ? (
        <div
            className="size-12 z-50 fixed  bottom-1/2 translate-y-1/2 left-5 rounded-full bg-dinoBotLightBlue border border-dinoBotVibrantBlue flex justify-center items-center text-dinoBotVibrantBlue cursor-pointer"
            onClick={onOpen}
        >
            <div>
                <FileText />
            </div>
        </div>
    ) : null
}
